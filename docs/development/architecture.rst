System Architecture
==================

This document provides a comprehensive overview of the CVE Feed Service architecture, including design patterns, data flow, and technical decisions.

Overview
--------

The CVE Feed Service follows a layered architecture pattern with clear separation of concerns, designed for scalability, maintainability, and testability.

**Architecture Principles**:
* **Separation of Concerns**: Clear boundaries between layers
* **Dependency Injection**: Loose coupling through DI
* **Async-First**: Non-blocking operations throughout
* **API-First**: RESTful API as the primary interface
* **Domain-Driven Design**: Business logic encapsulated in services
* **SOLID Principles**: Object-oriented design best practices

High-Level Architecture
----------------------

.. mermaid::

   graph TB
       subgraph "External Systems"
           NVD[NVD API]
           CLIENT[API Clients]
       end
       
       subgraph "CVE Feed Service"
           subgraph "API Layer"
               ROUTER[FastAPI Router]
               AUTH[Authentication]
               VALID[Validation]
           end
           
           subgraph "Business Logic Layer"
               APP_SVC[Application Service]
               COMP_SVC[Component Service]
               CVE_SVC[CVE Service]
               AUTH_SVC[Auth Service]
               INGEST_SVC[Ingestion Service]
           end
           
           subgraph "Data Access Layer"
               MODELS[SQLAlchemy Models]
               REPOS[Repository Pattern]
           end
           
           subgraph "Infrastructure Layer"
               DB[(PostgreSQL)]
               CACHE[Redis Cache]
               LOGS[Structured Logging]
           end
       end
       
       CLIENT --> ROUTER
       ROUTER --> AUTH
       AUTH --> AUTH_SVC
       ROUTER --> APP_SVC
       ROUTER --> COMP_SVC
       ROUTER --> CVE_SVC
       
       APP_SVC --> MODELS
       COMP_SVC --> MODELS
       CVE_SVC --> MODELS
       AUTH_SVC --> MODELS
       
       MODELS --> DB
       
       INGEST_SVC --> NVD
       INGEST_SVC --> MODELS
       
       APP_SVC --> CACHE
       CVE_SVC --> CACHE

Layered Architecture
-------------------

API Layer
~~~~~~~~~

**Responsibilities**:
* HTTP request/response handling
* Input validation and serialization
* Authentication and authorization
* Error handling and formatting
* API documentation generation

**Key Components**:

.. code-block:: python

   # FastAPI router structure
   src/cve_feed_service/api/v1/
   ├── router.py              # Main API router
   ├── endpoints/
   │   ├── applications.py    # Application endpoints
   │   ├── components.py      # Component endpoints
   │   ├── cves.py           # CVE endpoints
   │   └── auth.py           # Authentication endpoints
   └── dependencies.py       # Shared dependencies

**Design Patterns**:
* **Dependency Injection**: Services injected into endpoints
* **Request/Response Models**: Pydantic schemas for validation
* **Middleware Pattern**: Cross-cutting concerns (auth, logging)

Business Logic Layer
~~~~~~~~~~~~~~~~~~~

**Responsibilities**:
* Core business logic implementation
* Data validation and transformation
* Business rule enforcement
* Service orchestration
* Transaction management

**Service Architecture**:

.. mermaid::

   classDiagram
       class ApplicationService {
           -db: AsyncSession
           +create_application(data) Application
           +get_application(id) Application
           +list_applications(filters) List[Application]
           +update_application(id, data) Application
           +delete_application(id) bool
       }
       
       class ComponentService {
           -db: AsyncSession
           +create_component(app_id, data) Component
           +get_component(id) Component
           +list_components(app_id) List[Component]
           +create_cpe_mapping(comp_id, data) CPEMapping
       }
       
       class CVEService {
           -db: AsyncSession
           +get_cve_by_id(cve_id) CVE
           +list_cves(filters) List[CVE]
           +get_tailored_feed(app_id) List[CVE]
       }
       
       ApplicationService --> ComponentService
       CVEService --> ComponentService

**Service Patterns**:
* **Service Layer Pattern**: Encapsulates business logic
* **Unit of Work Pattern**: Transaction management
* **Repository Pattern**: Data access abstraction

Data Access Layer
~~~~~~~~~~~~~~~~

**Responsibilities**:
* Database schema definition
* ORM mapping and relationships
* Query optimization
* Data integrity constraints
* Migration management

**Model Architecture**:

.. mermaid::

   erDiagram
       User ||--o{ APIKey : has
       User ||--o{ Application : owns
       Application ||--o{ Component : contains
       Component ||--o{ CPEMapping : maps_to
       CVE ||--o{ CVECPEApplicability : applies_to
       CPEMapping ||--o{ CVECPEApplicability : matches
       
       User {
           uuid id PK
           string username UK
           string email UK
           string hashed_password
           string role
           boolean is_active
           datetime created_at
           datetime updated_at
           datetime deleted_at
       }
       
       Application {
           uuid id PK
           string name
           string description
           string environment
           string criticality
           datetime created_at
           datetime updated_at
           datetime deleted_at
       }
       
       Component {
           uuid id PK
           uuid application_id FK
           string name
           string version
           string vendor
           string component_type
           datetime created_at
           datetime updated_at
           datetime deleted_at
       }

**Data Patterns**:
* **Active Record Pattern**: Models with behavior
* **Soft Delete Pattern**: Logical deletion with timestamps
* **Audit Trail Pattern**: Created/updated timestamps
* **UUID Pattern**: Globally unique identifiers

Infrastructure Layer
~~~~~~~~~~~~~~~~~~~

**Responsibilities**:
* Database connection management
* Caching implementation
* Logging and monitoring
* Configuration management
* External service integration

**Infrastructure Components**:

.. code-block:: python

   # Infrastructure setup
   src/cve_feed_service/core/
   ├── config.py          # Configuration management
   ├── database.py        # Database setup
   ├── dependencies.py    # DI container
   ├── logging.py         # Structured logging
   └── cache.py          # Caching layer

Data Flow Architecture
---------------------

Request Processing Flow
~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant Router
       participant Auth
       participant Service
       participant Model
       participant Database
       
       Client->>Router: HTTP Request
       Router->>Auth: Validate Authentication
       Auth-->>Router: User Context
       Router->>Service: Business Logic Call
       Service->>Model: Data Operation
       Model->>Database: SQL Query
       Database-->>Model: Query Result
       Model-->>Service: Domain Object
       Service-->>Router: Response Data
       Router-->>Client: HTTP Response

CVE Ingestion Flow
~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant CLI
       participant IngestionService
       participant NVDClient
       participant Database
       participant NVD_API
       
       CLI->>IngestionService: Start Ingestion
       IngestionService->>NVDClient: Get CVE Data
       NVDClient->>NVD_API: API Request
       NVD_API-->>NVDClient: CVE JSON
       NVDClient-->>IngestionService: Parsed CVEs
       
       loop For each CVE batch
           IngestionService->>Database: Bulk Insert/Update
           IngestionService->>Database: Process CPE Applicability
       end
       
       IngestionService-->>CLI: Ingestion Complete

Vulnerability Feed Generation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   flowchart TD
       A[API Request] --> B[Get Application]
       B --> C[Load Components]
       C --> D[Extract CPE Mappings]
       D --> E[Query CVE Database]
       E --> F[Join CPE Applicability]
       F --> G[Apply Filters]
       G --> H[Paginate Results]
       H --> I[Return CVE Feed]
       
       subgraph "Optimization"
           J[Index on CPE Strings]
           K[Eager Loading]
           L[Query Caching]
       end
       
       E --> J
       C --> K
       G --> L

Design Patterns
--------------

Dependency Injection
~~~~~~~~~~~~~~~~~~~

**Implementation**:

.. code-block:: python

   # Dependency injection setup
   from fastapi import Depends
   from sqlalchemy.ext.asyncio import AsyncSession
   
   from ..core.database import get_db
   from ..core.auth import get_current_user
   
   
   async def get_application_service(
       db: AsyncSession = Depends(get_db)
   ) -> ApplicationService:
       return ApplicationService(db)
   
   
   @router.post("/applications")
   async def create_application(
       data: ApplicationCreate,
       current_user: User = Depends(get_current_user),
       app_service: ApplicationService = Depends(get_application_service)
   ):
       return await app_service.create_application(data)

**Benefits**:
* Loose coupling between components
* Easy testing with mock dependencies
* Clear dependency relationships
* Flexible configuration

Repository Pattern
~~~~~~~~~~~~~~~~~

**Implementation**:

.. code-block:: python

   # Abstract repository
   from abc import ABC, abstractmethod
   from typing import List, Optional
   from uuid import UUID
   
   
   class ApplicationRepository(ABC):
       @abstractmethod
       async def create(self, application: Application) -> Application:
           pass
       
       @abstractmethod
       async def get_by_id(self, id: UUID) -> Optional[Application]:
           pass
       
       @abstractmethod
       async def list(self, filters: dict) -> List[Application]:
           pass
   
   
   # SQLAlchemy implementation
   class SQLApplicationRepository(ApplicationRepository):
       def __init__(self, db: AsyncSession):
           self.db = db
       
       async def create(self, application: Application) -> Application:
           self.db.add(application)
           await self.db.commit()
           await self.db.refresh(application)
           return application

Unit of Work Pattern
~~~~~~~~~~~~~~~~~~~

**Implementation**:

.. code-block:: python

   # Unit of Work for transaction management
   class UnitOfWork:
       def __init__(self, db: AsyncSession):
           self.db = db
           self._repositories = {}
       
       async def __aenter__(self):
           return self
       
       async def __aexit__(self, exc_type, exc_val, exc_tb):
           if exc_type:
               await self.rollback()
           else:
               await self.commit()
       
       async def commit(self):
           await self.db.commit()
       
       async def rollback(self):
           await self.db.rollback()
       
       @property
       def applications(self) -> ApplicationRepository:
           if 'applications' not in self._repositories:
               self._repositories['applications'] = SQLApplicationRepository(self.db)
           return self._repositories['applications']

Service Layer Pattern
~~~~~~~~~~~~~~~~~~~~

**Implementation**:

.. code-block:: python

   # Service encapsulates business logic
   class ApplicationService:
       def __init__(self, db: AsyncSession):
           self.db = db
       
       async def create_application(self, data: ApplicationCreate) -> Application:
           # Business logic: validate uniqueness
           existing = await self._get_by_name_and_env(data.name, data.environment)
           if existing:
               raise ValueError(f"Application '{data.name}' already exists")
           
           # Create application
           application = Application(**data.model_dump())
           self.db.add(application)
           await self.db.commit()
           await self.db.refresh(application)
           
           # Business logic: log creation
           logger.info("Application created", application_id=application.id)
           
           return application

Performance Architecture
------------------------

Database Optimization
~~~~~~~~~~~~~~~~~~~~~

**Indexing Strategy**:

.. code-block:: sql

   -- Application indexes
   CREATE INDEX idx_applications_name_env ON applications(name, environment);
   CREATE INDEX idx_applications_created_at ON applications(created_at);
   
   -- Component indexes
   CREATE INDEX idx_components_app_id ON components(application_id);
   CREATE INDEX idx_components_name_version ON components(name, version);
   
   -- CVE indexes
   CREATE INDEX idx_cves_severity ON cves(cvss_v3_severity);
   CREATE INDEX idx_cves_published_date ON cves(published_date);
   
   -- CPE applicability indexes
   CREATE INDEX idx_cpe_applicability_cpe_string ON cve_cpe_applicability(cpe_string);

**Query Optimization**:

.. code-block:: python

   # Eager loading to avoid N+1 queries
   from sqlalchemy.orm import selectinload
   
   async def get_application_with_components(self, app_id: UUID):
       query = select(Application).where(Application.id == app_id).options(
           selectinload(Application.components).selectinload(Component.cpe_mappings)
       )
       result = await self.db.execute(query)
       return result.scalar_one_or_none()

Caching Strategy
~~~~~~~~~~~~~~~

**Multi-Level Caching**:

.. mermaid::

   graph TD
       A[API Request] --> B{Application Cache}
       B -->|Hit| C[Return Cached Data]
       B -->|Miss| D[Query Database]
       D --> E[Cache Result]
       E --> F[Return Data]
       
       G[CVE Feed Request] --> H{Feed Cache}
       H -->|Hit| I[Return Cached Feed]
       H -->|Miss| J[Generate Feed]
       J --> K[Cache Feed]
       K --> L[Return Feed]

**Cache Implementation**:

.. code-block:: python

   # Redis caching layer
   import redis.asyncio as redis
   from typing import Optional
   
   
   class CacheService:
       def __init__(self, redis_client: redis.Redis):
           self.redis = redis_client
       
       async def get(self, key: str) -> Optional[str]:
           return await self.redis.get(key)
       
       async def set(self, key: str, value: str, ttl: int = 3600):
           await self.redis.setex(key, ttl, value)
       
       async def delete(self, key: str):
           await self.redis.delete(key)

Security Architecture
--------------------

Authentication Flow
~~~~~~~~~~~~~~~~~~

.. mermaid::

   sequenceDiagram
       participant Client
       participant API
       participant AuthService
       participant Database
       participant JWT
       
       Client->>API: Login Request
       API->>AuthService: Validate Credentials
       AuthService->>Database: Check User
       Database-->>AuthService: User Data
       AuthService->>JWT: Generate Token
       JWT-->>AuthService: JWT Token
       AuthService-->>API: Token Response
       API-->>Client: JWT Token
       
       Note over Client: Store token securely
       
       Client->>API: API Request + JWT
       API->>JWT: Validate Token
       JWT-->>API: User Claims
       API->>API: Process Request
       API-->>Client: Response

Authorization Model
~~~~~~~~~~~~~~~~~~

**Role-Based Access Control**:

.. code-block:: python

   # Permission decorators
   from functools import wraps
   from fastapi import HTTPException, status
   
   
   def require_role(required_role: str):
       def decorator(func):
           @wraps(func)
           async def wrapper(*args, current_user: User, **kwargs):
               if current_user.role != required_role:
                   raise HTTPException(
                       status_code=status.HTTP_403_FORBIDDEN,
                       detail="Insufficient permissions"
                   )
               return await func(*args, current_user=current_user, **kwargs)
           return wrapper
       return decorator
   
   
   @require_role("it_admin")
   async def create_user(user_data: UserCreate, current_user: User):
       # Only IT admins can create users
       pass

Error Handling Architecture
--------------------------

Exception Hierarchy
~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Custom exception hierarchy
   class CVEFeedServiceException(Exception):
       """Base exception for CVE Feed Service."""
       pass
   
   
   class ValidationError(CVEFeedServiceException):
       """Validation error."""
       pass
   
   
   class NotFoundError(CVEFeedServiceException):
       """Resource not found error."""
       pass
   
   
   class ConflictError(CVEFeedServiceException):
       """Resource conflict error."""
       pass

Global Error Handler
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   # FastAPI exception handlers
   from fastapi import FastAPI, Request
   from fastapi.responses import JSONResponse
   
   
   @app.exception_handler(ValidationError)
   async def validation_error_handler(request: Request, exc: ValidationError):
       return JSONResponse(
           status_code=422,
           content={
               "detail": str(exc),
               "type": "validation_error"
           }
       )
   
   
   @app.exception_handler(NotFoundError)
   async def not_found_error_handler(request: Request, exc: NotFoundError):
       return JSONResponse(
           status_code=404,
           content={
               "detail": str(exc),
               "type": "not_found_error"
           }
       )

Monitoring and Observability
----------------------------

Structured Logging
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Structured logging setup
   import structlog
   
   
   # Configure structured logging
   structlog.configure(
       processors=[
           structlog.stdlib.filter_by_level,
           structlog.stdlib.add_logger_name,
           structlog.stdlib.add_log_level,
           structlog.stdlib.PositionalArgumentsFormatter(),
           structlog.processors.TimeStamper(fmt="iso"),
           structlog.processors.StackInfoRenderer(),
           structlog.processors.format_exc_info,
           structlog.processors.UnicodeDecoder(),
           structlog.processors.JSONRenderer()
       ],
       context_class=dict,
       logger_factory=structlog.stdlib.LoggerFactory(),
       wrapper_class=structlog.stdlib.BoundLogger,
       cache_logger_on_first_use=True,
   )
   
   
   # Usage in services
   logger = structlog.get_logger(__name__)
   
   
   async def create_application(self, data: ApplicationCreate):
       logger.info("Creating application", name=data.name, environment=data.environment)
       
       try:
           application = await self._create_application(data)
           logger.info("Application created successfully", application_id=application.id)
           return application
       except Exception as e:
           logger.error("Application creation failed", error=str(e), name=data.name)
           raise

Metrics Collection
~~~~~~~~~~~~~~~~~

.. code-block:: python

   # Metrics collection with Prometheus
   from prometheus_client import Counter, Histogram, Gauge
   
   
   # Define metrics
   request_count = Counter('http_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
   request_duration = Histogram('http_request_duration_seconds', 'HTTP request duration')
   active_connections = Gauge('active_database_connections', 'Active database connections')
   
   
   # Middleware for metrics collection
   @app.middleware("http")
   async def metrics_middleware(request: Request, call_next):
       start_time = time.time()
       
       response = await call_next(request)
       
       duration = time.time() - start_time
       request_count.labels(method=request.method, endpoint=request.url.path).inc()
       request_duration.observe(duration)
       
       return response

Next Steps
----------

* :doc:`testing` - Testing architecture and strategies
* :doc:`contributing` - Contributing guidelines and code standards
* :doc:`../services/index` - Detailed service documentation
* :doc:`../deployment/index` - Deployment architecture and strategies
