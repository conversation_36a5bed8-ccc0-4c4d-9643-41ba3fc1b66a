<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">42%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-18 18:21 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7___init___py.html">src/cve_feed_service/__init__.py</a></td>
                <td class="name left"><a href="z_82e9ebae9fe787a7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05fa98826f84bd0d___init___py.html">src/cve_feed_service/api/__init__.py</a></td>
                <td class="name left"><a href="z_05fa98826f84bd0d___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e128e9bbe61223c9___init___py.html">src/cve_feed_service/api/v1/__init__.py</a></td>
                <td class="name left"><a href="z_e128e9bbe61223c9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9___init___py.html">src/cve_feed_service/api/v1/endpoints/__init__.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html">src/cve_feed_service/api/v1/endpoints/applications.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>56</td>
                <td>36</td>
                <td>0</td>
                <td class="right" data-ratio="20 56">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>95</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="33 95">35%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>88</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="26 88">30%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html">src/cve_feed_service/api/v1/endpoints/cves.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>42</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="16 42">38%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e128e9bbe61223c9_router_py.html">src/cve_feed_service/api/v1/router.py</a></td>
                <td class="name left"><a href="z_e128e9bbe61223c9_router_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245___init___py.html">src/cve_feed_service/cli/__init__.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html">src/cve_feed_service/cli/cve_import.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>72</td>
                <td>72</td>
                <td>2</td>
                <td class="right" data-ratio="0 72">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_main_py.html">src/cve_feed_service/cli/main.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>8</td>
                <td>8</td>
                <td>2</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f___init___py.html">src/cve_feed_service/core/__init__.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="17 35">49%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t10">src/cve_feed_service/core/config.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t10"><data value='Settings'>Settings</data></a></td>
                <td>11</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="6 11">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html">src/cve_feed_service/core/config.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html">src/cve_feed_service/core/dependencies.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>55</td>
                <td>35</td>
                <td>0</td>
                <td class="right" data-ratio="20 55">36%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e___init___py.html">src/cve_feed_service/db/__init__.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t13">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t13"><data value='Base'>Base</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t19">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t19"><data value='SoftDeleteMixin'>SoftDeleteMixin</data></a></td>
                <td>4</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="1 4">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t41">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t41"><data value='TimestampMixin'>TimestampMixin</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t66">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t66"><data value='UUIDMixin'>UUIDMixin</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t80">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t80"><data value='BaseModel'>BaseModel</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>2</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html">src/cve_feed_service/db/database.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="10 23">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html">src/cve_feed_service/main.py</a></td>
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>8</td>
                <td>3</td>
                <td class="right" data-ratio="23 31">74%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5___init___py.html">src/cve_feed_service/models/__init__.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t13">src/cve_feed_service/models/application.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t13"><data value='Application'>Application</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t55">src/cve_feed_service/models/application.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t55"><data value='Component'>Component</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t108">src/cve_feed_service/models/application.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t108"><data value='CPEMapping'>CPEMapping</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html">src/cve_feed_service/models/application.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t13">src/cve_feed_service/models/cve.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t13"><data value='CVE'>CVE</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>2</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t93">src/cve_feed_service/models/cve.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t93"><data value='CVECPEApplicability'>CVECPEApplicability</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html">src/cve_feed_service/models/cve.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>43</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="43 43">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t12">src/cve_feed_service/models/user.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t12"><data value='UserRole'>UserRole</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t19">src/cve_feed_service/models/user.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t19"><data value='User'>User</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>2</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t70">src/cve_feed_service/models/user.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t70"><data value='APIKey'>APIKey</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html">src/cve_feed_service/models/user.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f___init___py.html">src/cve_feed_service/schemas/__init__.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t10">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t10"><data value='ApplicationBase'>ApplicationBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t21">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t21"><data value='ApplicationCreate'>ApplicationCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t26">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t26"><data value='ApplicationUpdate'>ApplicationUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t37">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t37"><data value='ApplicationResponse'>ApplicationResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t45">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t45"><data value='Config'>ApplicationResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t49">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t49"><data value='ComponentBase'>ComponentBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t59">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t59"><data value='ComponentCreate'>ComponentCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t64">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t64"><data value='ComponentUpdate'>ComponentUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t74">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t74"><data value='ComponentResponse'>ComponentResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t83">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t83"><data value='Config'>ComponentResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t87">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t87"><data value='CPEMappingBase'>CPEMappingBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t95">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t95"><data value='CPEMappingCreate'>CPEMappingCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t100">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t100"><data value='CPEMappingUpdate'>CPEMappingUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t108">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t108"><data value='CPEMappingResponse'>CPEMappingResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t117">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t117"><data value='Config'>CPEMappingResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t122">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t122"><data value='ComponentWithCPEResponse'>ComponentWithCPEResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t128">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html#t128"><data value='ApplicationWithComponentsResponse'>ApplicationWithComponentsResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>71</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="71 71">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t12">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t12"><data value='UserBase'>UserBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t21">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t21"><data value='UserCreate'>UserCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t27">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t27"><data value='UserUpdate'>UserUpdate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t36">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t36"><data value='UserResponse'>UserResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t44">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t44"><data value='Config'>UserResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t48">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t48"><data value='LoginRequest'>LoginRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t55">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t55"><data value='TokenResponse'>TokenResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t63">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t63"><data value='APIKeyBase'>APIKeyBase</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t69">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t69"><data value='APIKeyCreate'>APIKeyCreate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t74">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t74"><data value='APIKeyResponse'>APIKeyResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t84">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t84"><data value='Config'>APIKeyResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t88">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t88"><data value='APIKeyCreateResponse'>APIKeyCreateResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t94">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t94"><data value='UserWithAPIKeysResponse'>UserWithAPIKeysResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t100">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html#t100"><data value='PasswordChangeRequest'>PasswordChangeRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>51</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="51 51">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t10">src/cve_feed_service/schemas/cve.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t10"><data value='CVECPEApplicabilityResponse'>CVECPEApplicabilityResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t28">src/cve_feed_service/schemas/cve.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t28"><data value='Config'>CVECPEApplicabilityResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t32">src/cve_feed_service/schemas/cve.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t32"><data value='CVEResponse'>CVEResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t54">src/cve_feed_service/schemas/cve.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t54"><data value='Config'>CVEResponse.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t58">src/cve_feed_service/schemas/cve.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t58"><data value='CVEWithApplicabilityResponse'>CVEWithApplicabilityResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t64">src/cve_feed_service/schemas/cve.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t64"><data value='CVEFeedQuery'>CVEFeedQuery</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t75">src/cve_feed_service/schemas/cve.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html#t75"><data value='CVEFeedResponse'>CVEFeedResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html">src/cve_feed_service/schemas/cve.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>57</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="57 57">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7___init___py.html">src/cve_feed_service/services/__init__.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t17">src/cve_feed_service/services/application_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t17"><data value='ApplicationService'>ApplicationService</data></a></td>
                <td>51</td>
                <td>46</td>
                <td>0</td>
                <td class="right" data-ratio="5 51">10%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html">src/cve_feed_service/services/application_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t18">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t18"><data value='AuthService'>AuthService</data></a></td>
                <td>64</td>
                <td>64</td>
                <td>0</td>
                <td class="right" data-ratio="0 64">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t18">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t18"><data value='ComponentService'>ComponentService</data></a></td>
                <td>90</td>
                <td>90</td>
                <td>0</td>
                <td class="right" data-ratio="0 90">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t18">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t18"><data value='CVEIngestionService'>CVEIngestionService</data></a></td>
                <td>127</td>
                <td>127</td>
                <td>0</td>
                <td class="right" data-ratio="0 127">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t18">src/cve_feed_service/services/cve_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t18"><data value='CVEService'>CVEService</data></a></td>
                <td>58</td>
                <td>58</td>
                <td>0</td>
                <td class="right" data-ratio="0 58">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html">src/cve_feed_service/services/cve_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t17">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t17"><data value='NVDAPIClient'>NVDAPIClient</data></a></td>
                <td>85</td>
                <td>85</td>
                <td>0</td>
                <td class="right" data-ratio="0 85">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b___init___py.html">src/cve_feed_service/utils/__init__.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t7">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t7"><data value='CPEValidator'>CPEValidator</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t80">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t80"><data value='CPEMatcher'>CPEMatcher</data></a></td>
                <td>30</td>
                <td>30</td>
                <td>0</td>
                <td class="right" data-ratio="0 30">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>23</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="21 23">91%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1559</td>
                <td>910</td>
                <td>31</td>
                <td class="right" data-ratio="649 1559">42%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-18 18:21 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
