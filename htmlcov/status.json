{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.6.1", "globals": "a3dedb7eb03704fbc043682de3df4703", "files": {"z_82e9ebae9fe787a7___init___py": {"hash": "133f1a36b85ea9c8a6523834841137a4", "index": {"url": "z_82e9ebae9fe787a7___init___py.html", "file": "src/cve_feed_service/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 1, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_05fa98826f84bd0d___init___py": {"hash": "959c9f46b931aeb503e6092d3c47f7c4", "index": {"url": "z_05fa98826f84bd0d___init___py.html", "file": "src/cve_feed_service/api/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e128e9bbe61223c9___init___py": {"hash": "e73b5302b171b5ffd6692446a0fa7267", "index": {"url": "z_e128e9bbe61223c9___init___py.html", "file": "src/cve_feed_service/api/v1/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_143e20384287c6c9___init___py": {"hash": "c7899c000bbab9299663dde06d9dfb9d", "index": {"url": "z_143e20384287c6c9___init___py.html", "file": "src/cve_feed_service/api/v1/endpoints/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_143e20384287c6c9_applications_py": {"hash": "145058c6613ea93ac9242da008770c4d", "index": {"url": "z_143e20384287c6c9_applications_py.html", "file": "src/cve_feed_service/api/v1/endpoints/applications.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 56, "n_excluded": 0, "n_missing": 36, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_143e20384287c6c9_auth_py": {"hash": "70b39af85936dbff1587979d6513978d", "index": {"url": "z_143e20384287c6c9_auth_py.html", "file": "src/cve_feed_service/api/v1/endpoints/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 95, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_143e20384287c6c9_components_py": {"hash": "891df2625242167275325cd6985c9d0a", "index": {"url": "z_143e20384287c6c9_components_py.html", "file": "src/cve_feed_service/api/v1/endpoints/components.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 88, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_143e20384287c6c9_cves_py": {"hash": "9a1bca8e5ffff7ad00259399eaa07049", "index": {"url": "z_143e20384287c6c9_cves_py.html", "file": "src/cve_feed_service/api/v1/endpoints/cves.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 42, "n_excluded": 0, "n_missing": 26, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_e128e9bbe61223c9_router_py": {"hash": "18d85afef20a1c037b6ef97fc6817995", "index": {"url": "z_e128e9bbe61223c9_router_py.html", "file": "src/cve_feed_service/api/v1/router.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 7, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3aa71269b550f245___init___py": {"hash": "b7288cb6c222226257e2ffeab2022122", "index": {"url": "z_3aa71269b550f245___init___py.html", "file": "src/cve_feed_service/cli/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3aa71269b550f245_cve_import_py": {"hash": "45502dd8bcdceede6632851231bdd79f", "index": {"url": "z_3aa71269b550f245_cve_import_py.html", "file": "src/cve_feed_service/cli/cve_import.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 72, "n_excluded": 2, "n_missing": 72, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_3aa71269b550f245_main_py": {"hash": "da2bd28baed51eaffcdf5d79ea07ce6c", "index": {"url": "z_3aa71269b550f245_main_py.html", "file": "src/cve_feed_service/cli/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 2, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9553b3fd2f287b5f___init___py": {"hash": "211b6fc100578c98888b3bcf950a9919", "index": {"url": "z_9553b3fd2f287b5f___init___py.html", "file": "src/cve_feed_service/core/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9553b3fd2f287b5f_auth_py": {"hash": "6a4bca3811ddb5afcdfe2610e7250a72", "index": {"url": "z_9553b3fd2f287b5f_auth_py.html", "file": "src/cve_feed_service/core/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 0, "n_missing": 18, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9553b3fd2f287b5f_config_py": {"hash": "5c4b6bb48e37ea0dc9fd996746824a3b", "index": {"url": "z_9553b3fd2f287b5f_config_py.html", "file": "src/cve_feed_service/core/config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 54, "n_excluded": 0, "n_missing": 5, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_9553b3fd2f287b5f_dependencies_py": {"hash": "41bab183834ae5853b0bd7d0b49bc9fc", "index": {"url": "z_9553b3fd2f287b5f_dependencies_py.html", "file": "src/cve_feed_service/core/dependencies.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 55, "n_excluded": 0, "n_missing": 35, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ab874df1646c505e___init___py": {"hash": "2f3e1325b15ad7335dbb4dc50d374cf3", "index": {"url": "z_ab874df1646c505e___init___py.html", "file": "src/cve_feed_service/db/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ab874df1646c505e_base_py": {"hash": "c9e17788900f8f05451dc2ca8332dfa5", "index": {"url": "z_ab874df1646c505e_base_py.html", "file": "src/cve_feed_service/db/base.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 3, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_ab874df1646c505e_database_py": {"hash": "51d09f7e38e9bd3c080beb122a2db9c9", "index": {"url": "z_ab874df1646c505e_database_py.html", "file": "src/cve_feed_service/db/database.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 23, "n_excluded": 0, "n_missing": 13, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_82e9ebae9fe787a7_main_py": {"hash": "7892a8c5187db9c5dd356bb707322952", "index": {"url": "z_82e9ebae9fe787a7_main_py.html", "file": "src/cve_feed_service/main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 31, "n_excluded": 3, "n_missing": 8, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18bb95a27137c5b5___init___py": {"hash": "bbf452073f27e5d8822fc2c756f84d6d", "index": {"url": "z_18bb95a27137c5b5___init___py.html", "file": "src/cve_feed_service/models/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 4, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18bb95a27137c5b5_application_py": {"hash": "a98093581bbcb7ecbef76d5a21de19bc", "index": {"url": "z_18bb95a27137c5b5_application_py.html", "file": "src/cve_feed_service/models/application.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 35, "n_excluded": 9, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18bb95a27137c5b5_cve_py": {"hash": "2a1a1acb02cdfec0ef2daa4c103dbf8e", "index": {"url": "z_18bb95a27137c5b5_cve_py.html", "file": "src/cve_feed_service/models/cve.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 45, "n_excluded": 6, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_18bb95a27137c5b5_user_py": {"hash": "d2a806f87c414fb5b38477f24310822f", "index": {"url": "z_18bb95a27137c5b5_user_py.html", "file": "src/cve_feed_service/models/user.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 6, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_495b01bbf647992f___init___py": {"hash": "6d28902e24b872a8509a4969fdfa6284", "index": {"url": "z_495b01bbf647992f___init___py.html", "file": "src/cve_feed_service/schemas/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 3, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_495b01bbf647992f_application_py": {"hash": "15b08f0041f087f182a8de7cb509a24d", "index": {"url": "z_495b01bbf647992f_application_py.html", "file": "src/cve_feed_service/schemas/application.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 71, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_495b01bbf647992f_auth_py": {"hash": "3deab85e1dd4edd3f2dd1d5402f71c83", "index": {"url": "z_495b01bbf647992f_auth_py.html", "file": "src/cve_feed_service/schemas/auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 51, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_495b01bbf647992f_cve_py": {"hash": "b8215bc05a2aeeb3306cd0ea7a19b7a4", "index": {"url": "z_495b01bbf647992f_cve_py.html", "file": "src/cve_feed_service/schemas/cve.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 57, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_88b6e79fdcf0d8f7___init___py": {"hash": "35b9b15937405f720878112fa2d0c5ca", "index": {"url": "z_88b6e79fdcf0d8f7___init___py.html", "file": "src/cve_feed_service/services/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_88b6e79fdcf0d8f7_application_service_py": {"hash": "5ceb1c31f3031e2357a651e5c32edc99", "index": {"url": "z_88b6e79fdcf0d8f7_application_service_py.html", "file": "src/cve_feed_service/services/application_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 67, "n_excluded": 0, "n_missing": 46, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_88b6e79fdcf0d8f7_auth_service_py": {"hash": "14d7cc9e14388c6b306137ae61644296", "index": {"url": "z_88b6e79fdcf0d8f7_auth_service_py.html", "file": "src/cve_feed_service/services/auth_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 86, "n_excluded": 0, "n_missing": 64, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_88b6e79fdcf0d8f7_component_service_py": {"hash": "1b0fbdb6d16a5ba9653e50a27ca8b532", "index": {"url": "z_88b6e79fdcf0d8f7_component_service_py.html", "file": "src/cve_feed_service/services/component_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 111, "n_excluded": 0, "n_missing": 90, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_88b6e79fdcf0d8f7_cve_ingestion_service_py": {"hash": "a07904933c1ce508473d211a20ae54b4", "index": {"url": "z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html", "file": "src/cve_feed_service/services/cve_ingestion_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 150, "n_excluded": 0, "n_missing": 150, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_88b6e79fdcf0d8f7_cve_service_py": {"hash": "8b9b021d4e19ab1e6fd4746842a59a9f", "index": {"url": "z_88b6e79fdcf0d8f7_cve_service_py.html", "file": "src/cve_feed_service/services/cve_service.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 74, "n_excluded": 0, "n_missing": 58, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_88b6e79fdcf0d8f7_nvd_client_py": {"hash": "ac226d354d7539eac1df9b9b9857d8c1", "index": {"url": "z_88b6e79fdcf0d8f7_nvd_client_py.html", "file": "src/cve_feed_service/services/nvd_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 105, "n_excluded": 0, "n_missing": 105, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a5fe795f0915891b___init___py": {"hash": "d937595a3cf6dc7e97434aabd03a56d4", "index": {"url": "z_a5fe795f0915891b___init___py.html", "file": "src/cve_feed_service/utils/__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 0, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_a5fe795f0915891b_cpe_utils_py": {"hash": "1eb31373d41d965a940d103fce45d313", "index": {"url": "z_a5fe795f0915891b_cpe_utils_py.html", "file": "src/cve_feed_service/utils/cpe_utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 65, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}