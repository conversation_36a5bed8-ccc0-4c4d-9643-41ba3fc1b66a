<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_8e611ae1.css" type="text/css">
    <script src="coverage_html_cb_6fb7b396.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">41%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-18 20:08 +0200
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7___init___py.html">src/cve_feed_service/__init__.py</a></td>
                <td class="name left"><a href="z_82e9ebae9fe787a7___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_05fa98826f84bd0d___init___py.html">src/cve_feed_service/api/__init__.py</a></td>
                <td class="name left"><a href="z_05fa98826f84bd0d___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e128e9bbe61223c9___init___py.html">src/cve_feed_service/api/v1/__init__.py</a></td>
                <td class="name left"><a href="z_e128e9bbe61223c9___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9___init___py.html">src/cve_feed_service/api/v1/endpoints/__init__.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t30">src/cve_feed_service/api/v1/endpoints/applications.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t30"><data value='create_application'>create_application</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t56">src/cve_feed_service/api/v1/endpoints/applications.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t56"><data value='list_applications'>list_applications</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t83">src/cve_feed_service/api/v1/endpoints/applications.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t83"><data value='get_application'>get_application</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t109">src/cve_feed_service/api/v1/endpoints/applications.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t109"><data value='update_application'>update_application</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t142">src/cve_feed_service/api/v1/endpoints/applications.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html#t142"><data value='delete_application'>delete_application</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html">src/cve_feed_service/api/v1/endpoints/applications.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_applications_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t40">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t40"><data value='login'>login</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t80">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t80"><data value='get_current_user_info'>get_current_user_info</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t97">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t97"><data value='change_password'>change_password</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t126">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t126"><data value='create_user'>create_user</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t153">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t153"><data value='list_users'>list_users</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t170">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t170"><data value='update_user'>update_user</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t206">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t206"><data value='create_api_key'>create_api_key</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t236">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t236"><data value='list_api_keys'>list_api_keys</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t253">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html#t253"><data value='delete_api_key'>delete_api_key</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html">src/cve_feed_service/api/v1/endpoints/auth.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t33">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t33"><data value='create_component'>create_component</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t60">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t60"><data value='list_application_components'>list_application_components</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t87">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t87"><data value='get_component'>get_component</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t112">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t112"><data value='update_component'>update_component</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t145">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t145"><data value='delete_component'>delete_component</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t172">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t172"><data value='create_cpe_mapping'>create_cpe_mapping</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t199">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t199"><data value='update_cpe_mapping'>update_cpe_mapping</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t232">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html#t232"><data value='delete_cpe_mapping'>delete_cpe_mapping</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html">src/cve_feed_service/api/v1/endpoints/components.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_components_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>26</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="26 26">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html#t24">src/cve_feed_service/api/v1/endpoints/cves.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html#t24"><data value='get_cve_feed'>get_cve_feed</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html#t78">src/cve_feed_service/api/v1/endpoints/cves.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html#t78"><data value='get_cve'>get_cve</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html#t103">src/cve_feed_service/api/v1/endpoints/cves.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html#t103"><data value='list_cves'>list_cves</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html">src/cve_feed_service/api/v1/endpoints/cves.py</a></td>
                <td class="name left"><a href="z_143e20384287c6c9_cves_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_e128e9bbe61223c9_router_py.html">src/cve_feed_service/api/v1/router.py</a></td>
                <td class="name left"><a href="z_e128e9bbe61223c9_router_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245___init___py.html">src/cve_feed_service/cli/__init__.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t22">src/cve_feed_service/cli/cve_import.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t22"><data value='bulk_import'>bulk_import</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t31">src/cve_feed_service/cli/cve_import.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t31"><data value='incremental_update'>incremental_update</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t39">src/cve_feed_service/cli/cve_import.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t39"><data value='import_single'>import_single</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t46">src/cve_feed_service/cli/cve_import.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t46"><data value='bulk_import'>_bulk_import</data></a></td>
                <td>19</td>
                <td>19</td>
                <td>0</td>
                <td class="right" data-ratio="0 19">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t79">src/cve_feed_service/cli/cve_import.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t79"><data value='incremental_update'>_incremental_update</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t97">src/cve_feed_service/cli/cve_import.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html#t97"><data value='import_single'>_import_single</data></a></td>
                <td>18</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="0 18">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html">src/cve_feed_service/cli/cve_import.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_cve_import_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>21</td>
                <td>2</td>
                <td class="right" data-ratio="0 21">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_main_py.html#t14">src/cve_feed_service/cli/main.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_main_py.html#t14"><data value='version'>version</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_3aa71269b550f245_main_py.html">src/cve_feed_service/cli/main.py</a></td>
                <td class="name left"><a href="z_3aa71269b550f245_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>6</td>
                <td>6</td>
                <td>2</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f___init___py.html">src/cve_feed_service/core/__init__.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t25">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t25"><data value='verify_password'>verify_password</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t30">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t30"><data value='get_password_hash'>get_password_hash</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t35">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t35"><data value='create_access_token'>create_access_token</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t50">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t50"><data value='verify_token'>verify_token</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t60">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t60"><data value='generate_api_key'>generate_api_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t65">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t65"><data value='hash_api_key'>hash_api_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t70">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t70"><data value='verify_api_key'>verify_api_key</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t75">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html#t75"><data value='get_current_user'>get_current_user</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html">src/cve_feed_service/core/auth.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t64">src/cve_feed_service/core/config.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t64"><data value='validate_environment'>Settings.validate_environment</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t73">src/cve_feed_service/core/config.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t73"><data value='validate_log_level'>Settings.validate_log_level</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t81">src/cve_feed_service/core/config.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t81"><data value='is_development'>Settings.is_development</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t86">src/cve_feed_service/core/config.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t86"><data value='is_production'>Settings.is_production</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t91">src/cve_feed_service/core/config.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t91"><data value='is_testing'>Settings.is_testing</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t97">src/cve_feed_service/core/config.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html#t97"><data value='get_settings'>get_settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html">src/cve_feed_service/core/config.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t23">src/cve_feed_service/core/dependencies.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t23"><data value='get_current_user_from_token'>get_current_user_from_token</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t53">src/cve_feed_service/core/dependencies.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t53"><data value='get_current_user_from_api_key'>get_current_user_from_api_key</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t96">src/cve_feed_service/core/dependencies.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t96"><data value='get_current_user'>get_current_user</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t104">src/cve_feed_service/core/dependencies.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t104"><data value='require_authentication'>require_authentication</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t118">src/cve_feed_service/core/dependencies.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t118"><data value='require_admin'>require_admin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t131">src/cve_feed_service/core/dependencies.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t131"><data value='require_analyst_or_admin'>require_analyst_or_admin</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t145">src/cve_feed_service/core/dependencies.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html#t145"><data value='check_permission'>check_permission</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html">src/cve_feed_service/core/dependencies.py</a></td>
                <td class="name left"><a href="z_9553b3fd2f287b5f_dependencies_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e___init___py.html">src/cve_feed_service/db/__init__.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t23">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t23"><data value='deleted_at'>SoftDeleteMixin.deleted_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t27">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t27"><data value='soft_delete'>SoftDeleteMixin.soft_delete</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t31">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t31"><data value='restore'>SoftDeleteMixin.restore</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t36">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t36"><data value='is_deleted'>SoftDeleteMixin.is_deleted</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t45">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t45"><data value='created_at'>TimestampMixin.created_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t55">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t55"><data value='updated_at'>TimestampMixin.updated_at</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t70">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t70"><data value='id'>UUIDMixin.id</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t85">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t85"><data value='repr__'>BaseModel.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t89">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html#t89"><data value='to_dict'>BaseModel.to_dict</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html">src/cve_feed_service/db/base.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_base_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>1</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html#t31">src/cve_feed_service/db/database.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html#t31"><data value='get_db'>get_db</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html#t43">src/cve_feed_service/db/database.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html#t43"><data value='create_tables'>create_tables</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html#t51">src/cve_feed_service/db/database.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html#t51"><data value='drop_tables'>drop_tables</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html">src/cve_feed_service/db/database.py</a></td>
                <td class="name left"><a href="z_ab874df1646c505e_database_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html#t38">src/cve_feed_service/main.py</a></td>
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html#t38"><data value='lifespan'>lifespan</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html#t52">src/cve_feed_service/main.py</a></td>
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html#t52"><data value='create_application'>create_application</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html#t78">src/cve_feed_service/main.py</a></td>
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html#t78"><data value='health_check'>create_application.health_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html#t83">src/cve_feed_service/main.py</a></td>
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html#t83"><data value='readiness_check'>create_application.readiness_check</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html">src/cve_feed_service/main.py</a></td>
                <td class="name left"><a href="z_82e9ebae9fe787a7_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>15</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="15 15">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5___init___py.html">src/cve_feed_service/models/__init__.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t50">src/cve_feed_service/models/application.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t50"><data value='repr__'>Application.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t103">src/cve_feed_service/models/application.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t103"><data value='repr__'>Component.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t143">src/cve_feed_service/models/application.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html#t143"><data value='repr__'>CPEMapping.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html">src/cve_feed_service/models/application.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_application_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>3</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t77">src/cve_feed_service/models/cve.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t77"><data value='repr__'>CVE.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t82">src/cve_feed_service/models/cve.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t82"><data value='severity_score'>CVE.severity_score</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t87">src/cve_feed_service/models/cve.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t87"><data value='severity_level'>CVE.severity_level</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t148">src/cve_feed_service/models/cve.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html#t148"><data value='repr__'>CVECPEApplicability.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html">src/cve_feed_service/models/cve.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_cve_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>42</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="42 42">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t55">src/cve_feed_service/models/user.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t55"><data value='repr__'>User.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t60">src/cve_feed_service/models/user.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t60"><data value='is_admin'>User.is_admin</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t65">src/cve_feed_service/models/user.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t65"><data value='is_analyst'>User.is_analyst</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t104">src/cve_feed_service/models/user.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html#t104"><data value='repr__'>APIKey.__repr__</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html">src/cve_feed_service/models/user.py</a></td>
                <td class="name left"><a href="z_18bb95a27137c5b5_user_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>31</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="31 31">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f___init___py.html">src/cve_feed_service/schemas/__init__.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html">src/cve_feed_service/schemas/application.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_application_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>68</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="68 68">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html">src/cve_feed_service/schemas/auth.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>49</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="49 49">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html">src/cve_feed_service/schemas/cve.py</a></td>
                <td class="name left"><a href="z_495b01bbf647992f_cve_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>55</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="55 55">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7___init___py.html">src/cve_feed_service/services/__init__.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t20">src/cve_feed_service/services/application_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t20"><data value='init__'>ApplicationService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t24">src/cve_feed_service/services/application_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t24"><data value='create_application'>ApplicationService.create_application</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t51">src/cve_feed_service/services/application_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t51"><data value='get_application'>ApplicationService.get_application</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t74">src/cve_feed_service/services/application_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t74"><data value='list_applications'>ApplicationService.list_applications</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t94">src/cve_feed_service/services/application_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t94"><data value='update_application'>ApplicationService.update_application</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t138">src/cve_feed_service/services/application_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html#t138"><data value='delete_application'>ApplicationService.delete_application</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html">src/cve_feed_service/services/application_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_application_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>16</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="16 16">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t21">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t21"><data value='init__'>AuthService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t25">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t25"><data value='authenticate_user'>AuthService.authenticate_user</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t43">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t43"><data value='create_user'>AuthService.create_user</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t84">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t84"><data value='get_user_by_id'>AuthService.get_user_by_id</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t97">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t97"><data value='get_user_with_api_keys'>AuthService.get_user_with_api_keys</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t112">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t112"><data value='list_users'>AuthService.list_users</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t122">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t122"><data value='update_user'>AuthService.update_user</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t153">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t153"><data value='change_password'>AuthService.change_password</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t165">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t165"><data value='create_api_key'>AuthService.create_api_key</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t202">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t202"><data value='list_user_api_keys'>AuthService.list_user_api_keys</data></a></td>
                <td>2</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="0 2">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t215">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html#t215"><data value='delete_api_key'>AuthService.delete_api_key</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html">src/cve_feed_service/services/auth_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_auth_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t21">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t21"><data value='init__'>ComponentService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t25">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t25"><data value='validate_cpe_string'>ComponentService._validate_cpe_string</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t29">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t29"><data value='create_component'>ComponentService.create_component</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t76">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t76"><data value='get_component'>ComponentService.get_component</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t88">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t88"><data value='list_components'>ComponentService.list_components</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t113">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t113"><data value='update_component'>ComponentService.update_component</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t154">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t154"><data value='delete_component'>ComponentService.delete_component</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t173">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t173"><data value='create_cpe_mapping'>ComponentService.create_cpe_mapping</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t214">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t214"><data value='update_cpe_mapping'>ComponentService.update_cpe_mapping</data></a></td>
                <td>17</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="0 17">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t261">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html#t261"><data value='delete_cpe_mapping'>ComponentService.delete_cpe_mapping</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html">src/cve_feed_service/services/component_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_component_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t21">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t21"><data value='init__'>CVEIngestionService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t25">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t25"><data value='parse_cvss_data'>CVEIngestionService._parse_cvss_data</data></a></td>
                <td>16</td>
                <td>16</td>
                <td>0</td>
                <td class="right" data-ratio="0 16">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t61">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t61"><data value='parse_cwe_data'>CVEIngestionService._parse_cwe_data</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t76">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t76"><data value='parse_references'>CVEIngestionService._parse_references</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t90">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t90"><data value='parse_description'>CVEIngestionService._parse_description</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t100">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t100"><data value='parse_dates'>CVEIngestionService._parse_dates</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t122">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t122"><data value='create_or_update_cve'>CVEIngestionService._create_or_update_cve</data></a></td>
                <td>26</td>
                <td>26</td>
                <td>0</td>
                <td class="right" data-ratio="0 26">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t181">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t181"><data value='process_cpe_applicability'>CVEIngestionService._process_cpe_applicability</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t216">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t216"><data value='ingest_cve'>CVEIngestionService.ingest_cve</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t223">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t223"><data value='bulk_ingest_cves'>CVEIngestionService.bulk_ingest_cves</data></a></td>
                <td>14</td>
                <td>14</td>
                <td>0</td>
                <td class="right" data-ratio="0 14">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t247">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t247"><data value='perform_bulk_import'>CVEIngestionService.perform_bulk_import</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t265">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html#t265"><data value='perform_incremental_update'>CVEIngestionService.perform_incremental_update</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html">src/cve_feed_service/services/cve_ingestion_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_ingestion_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>23</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="0 23">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t21">src/cve_feed_service/services/cve_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t21"><data value='init__'>CVEService.__init__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t25">src/cve_feed_service/services/cve_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t25"><data value='get_cve_by_id'>CVEService.get_cve_by_id</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t37">src/cve_feed_service/services/cve_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t37"><data value='list_cves'>CVEService.list_cves</data></a></td>
                <td>28</td>
                <td>28</td>
                <td>0</td>
                <td class="right" data-ratio="0 28">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t89">src/cve_feed_service/services/cve_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t89"><data value='get_tailored_feed'>CVEService.get_tailored_feed</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t118">src/cve_feed_service/services/cve_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t118"><data value='get_application_cve_feed'>CVEService._get_application_cve_feed</data></a></td>
                <td>22</td>
                <td>22</td>
                <td>0</td>
                <td class="right" data-ratio="0 22">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t221">src/cve_feed_service/services/cve_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html#t221"><data value='get_tailored_cve_feed'>CVEService.get_tailored_cve_feed</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html">src/cve_feed_service/services/cve_service.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_cve_service_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t20">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t20"><data value='init__'>NVDAPIClient.__init__</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t41">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t41"><data value='aenter__'>NVDAPIClient.__aenter__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t45">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t45"><data value='aexit__'>NVDAPIClient.__aexit__</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t49">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t49"><data value='rate_limit'>NVDAPIClient._rate_limit</data></a></td>
                <td>7</td>
                <td>7</td>
                <td>0</td>
                <td class="right" data-ratio="0 7">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t65">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t65"><data value='make_request'>NVDAPIClient._make_request</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t87">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t87"><data value='get_cves'>NVDAPIClient.get_cves</data></a></td>
                <td>12</td>
                <td>12</td>
                <td>0</td>
                <td class="right" data-ratio="0 12">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t129">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t129"><data value='get_cve_by_id'>NVDAPIClient.get_cve_by_id</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t150">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t150"><data value='get_recent_cves'>NVDAPIClient.get_recent_cves</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t186">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html#t186"><data value='get_cves_by_date_range'>NVDAPIClient.get_cves_by_date_range</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html">src/cve_feed_service/services/nvd_client.py</a></td>
                <td class="name left"><a href="z_88b6e79fdcf0d8f7_nvd_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>20</td>
                <td>0</td>
                <td class="right" data-ratio="0 20">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b___init___py.html">src/cve_feed_service/utils/__init__.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t16">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t16"><data value='validate_cpe_string'>CPEValidator.validate_cpe_string</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t31">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t31"><data value='parse_cpe_string'>CPEValidator.parse_cpe_string</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t63">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t63"><data value='normalize_cpe_component'>CPEValidator.normalize_cpe_component</data></a></td>
                <td>3</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="0 3">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t84">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t84"><data value='cpe_matches'>CPEMatcher.cpe_matches</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t116">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t116"><data value='component_matches'>CPEMatcher._component_matches</data></a></td>
                <td>5</td>
                <td>5</td>
                <td>0</td>
                <td class="right" data-ratio="0 5">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t138">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t138"><data value='extract_cpe_base'>CPEMatcher.extract_cpe_base</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t154">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t154"><data value='generate_cpe_patterns'>CPEMatcher.generate_cpe_patterns</data></a></td>
                <td>11</td>
                <td>11</td>
                <td>0</td>
                <td class="right" data-ratio="0 11">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t187">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t187"><data value='validate_cpe_string'>validate_cpe_string</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t199">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html#t199"><data value='parse_cpe_string'>parse_cpe_string</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html">src/cve_feed_service/utils/cpe_utils.py</a></td>
                <td class="name left"><a href="z_a5fe795f0915891b_cpe_utils_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>1564</td>
                <td>923</td>
                <td>31</td>
                <td class="right" data-ratio="641 1564">41%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.6.1">coverage.py v7.6.1</a>,
            created at 2025-06-18 20:08 +0200
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
