"""Test configuration and fixtures."""

import asyncio
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import StaticPool
from httpx import AsyncClient

from src.cve_feed_service.db.base import Base
from src.cve_feed_service.main import app


@pytest.fixture(scope="session")
async def test_engine():
    """Create test database engine."""
    # Use in-memory SQLite for unit tests
    engine = create_async_engine(
        "sqlite+aiosqlite:///:memory:",
        poolclass=StaticPool,
        connect_args={
            "check_same_thread": False,
            "isolation_level": None,
        },
        echo=False,  # Set to True for SQL debugging
    )
    
    # Create all tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    await engine.dispose()


@pytest.fixture
async def db_session(test_engine):
    """Create database session for each test with automatic rollback."""
    async with AsyncSession(test_engine) as session:
        # Start a transaction
        transaction = await session.begin()
        
        yield session
        
        # Rollback transaction to ensure test isolation
        await transaction.rollback()


@pytest.fixture
async def async_client():
    """Create async test client."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.fixture
def sample_application_data():
    """Sample application creation data."""
    return {
        "name": "Test Application",
        "description": "Test application for testing",
        "environment": "test",
        "criticality": "medium",
        "owner": "Test Team"
    }


@pytest.fixture
def sample_component_data():
    """Sample component creation data."""
    return {
        "name": "nginx",
        "version": "1.20.1",
        "vendor": "nginx",
        "component_type": "web_server",
        "description": "Web server component"
    }


@pytest.fixture
def sample_cpe_mapping_data():
    """Sample CPE mapping creation data."""
    return {
        "cpe_string": "cpe:2.3:a:nginx:nginx:1.20.1:*:*:*:*:*:*:*",
        "confidence": 1.0,
        "mapping_source": "official"
    }
